{"version": 3, "file": "Parser.js", "sourceRoot": "", "sources": ["../../../src/parser/Parser.ts"], "names": [], "mappings": ";;;AAAA,uCAAoC;AACpC,2CAAwC;AAGxC,mCAAgC;AAMhC,MAAa,MAAM;IAcf,YAAmB,aAA4B;QAC3C,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,SAAS,GAAG,IAAI,qBAAS,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IACvD,CAAC;IAhBO,MAAM,CAAC,SAAS,CAAC,IAAY;QACjC,0DAA0D;QAC1D,gDAAgD;QAChD,IAAI,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,MAAM,EAAE;YACvC,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;SACxB;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAWM,KAAK,CAAC,IAAY,EAAE,WAAoB;QAC3C,MAAM,OAAO,GAAG,IAAI,iBAAO,CAAC;YACxB,IAAI,EAAE,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC;YAC5B,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,WAAW;SACd,CAAC,CAAC;QACH,IAAI,IAAI,CAAC,aAAa,CAAC,gBAAgB,EAAE;YACrC,OAAO,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;SAC1C;QACD,OAAO,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;IAC9C,CAAC;IAEO,oBAAoB,CAAC,OAAgB;QACzC,MAAM,IAAI,GAAe,EAAE,CAAC;QAC5B,IAAI,cAAc,GAAG,IAAI,CAAC;QAC1B,OAAO,cAAc,EAAE;YACnB,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;SACjD;QACD,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC;IACxC,CAAC;IAEO,iBAAiB,CAAC,OAAgB;QACtC,MAAM,EAAE,aAAa,EAAE,GAAG,IAAI,CAAC;QAC/B,MAAM,IAAI,GAAe,EAAE,CAAC;QAC5B,KAAK,IAAI,SAAS,GAAG,OAAO,CAAC,kBAAkB,EAAE,SAAS,KAAK,IAAI,EAAE,SAAS,GAAG,OAAO,CAAC,kBAAkB,EAAE;YACzG,IAAI,aAAK,CAAC,cAAc,CAAC,SAAS,EAAE,aAAa,CAAC,EAAE;gBAChD,MAAM,MAAM,GAAG,OAAO,CAAC,eAAe,EAAE,CAAC;gBACzC,IAAI,MAAM,KAAK,IAAI,EAAE;oBACjB,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,cAAc,EAAE,IAAI,EAAE,CAAC;iBACjD;gBACD,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE;oBAC5B,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,cAAc,EAAE,IAAI,EAAE,CAAC;iBACjD;gBACD,OAAO,CAAC,gBAAgB,EAAE,CAAC;aAC9B;iBAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE;gBACtC,MAAM;aACT;SACJ;QACD,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC;IACxC,CAAC;IAEO,QAAQ,CAAC,OAAgB,EAAE,IAAgB;QAC/C,MAAM,SAAS,GAAG,OAAO,CAAC,iBAAiB,CAAC;QAC5C,IAAI,CAAC,SAAS,EAAE;YACZ,OAAO,KAAK,CAAC;SAChB;QACD,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAC1C,IAAI,GAAG,KAAK,IAAI,EAAE;YACd,OAAO,KAAK,CAAC;SAChB;QACD,IAAI,IAAI,CAAC,aAAa,CAAC,WAAW,IAAI,qBAAS,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;YAC7D,OAAO,IAAI,CAAC;SACf;QACD,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACf,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ;AA3ED,wBA2EC"}